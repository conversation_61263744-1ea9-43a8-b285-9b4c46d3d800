import { create } from 'zustand'
import { devtools, persist } from 'zustand/middleware'
import { FeedType, FeedItem, FeedFilters, FeedPreferences } from '@/types/feed'

interface FeedState {
  // Current feed state
  currentFeed: FeedType
  items: FeedItem[]
  loading: boolean
  error: string | null
  hasMore: boolean
  nextCursor: string | null
  
  // Feed management
  currentIndex: number
  preloadedItems: Set<string>
  filters: FeedFilters
  preferences: FeedPreferences
  
  // Cache management
  feedCache: Map<string, FeedItem[]>
  lastFetchTime: Map<string, number>
  
  // Actions
  setCurrentFeed: (feedType: FeedType) => void
  setItems: (items: FeedItem[]) => void
  addItems: (items: FeedItem[]) => void
  updateItem: (itemId: string, updates: Partial<FeedItem>) => void
  removeItem: (itemId: string) => void
  setCurrentIndex: (index: number) => void
  setLoading: (loading: boolean) => void
  setError: (error: string | null) => void
  setHasMore: (hasMore: boolean) => void
  setNextCursor: (cursor: string | null) => void
  setFilters: (filters: Partial<FeedFilters>) => void
  setPreferences: (preferences: Partial<FeedPreferences>) => void
  addPreloadedItem: (itemId: string) => void
  removePreloadedItem: (itemId: string) => void
  clearFeed: () => void
  cacheFeed: (feedType: FeedType, items: FeedItem[]) => void
  getCachedFeed: (feedType: FeedType) => FeedItem[] | null
  clearCache: () => void
  reset: () => void
}

const defaultPreferences: FeedPreferences = {
  autoPlay: true,
  muteByDefault: true,
  showCaptions: false,
  preferredQuality: 'auto',
  enableHapticFeedback: true,
  preloadDistance: 3,
  offloadDistance: 10,
}

const defaultFilters: FeedFilters = {
  moods: [],
  contentTypes: [],
  creators: [],
  personas: [],
  isExperimental: false,
}

export const useFeedStore = create<FeedState>()(
  devtools(
    persist(
      (set, get) => ({
        // Initial state
        currentFeed: FeedType.DISCOVER,
        items: [],
        loading: false,
        error: null,
        hasMore: true,
        nextCursor: null,
        currentIndex: 0,
        preloadedItems: new Set(),
        filters: defaultFilters,
        preferences: defaultPreferences,
        feedCache: new Map(),
        lastFetchTime: new Map(),

        // Actions
        setCurrentFeed: (feedType: FeedType) => {
          set({ currentFeed: feedType, currentIndex: 0 })
        },

        setItems: (items: FeedItem[]) => {
          set({ items, currentIndex: 0, preloadedItems: new Set() })
        },

        addItems: (newItems: FeedItem[]) => {
          set((state) => ({
            items: [...state.items, ...newItems],
          }))
        },

        updateItem: (itemId: string, updates: Partial<FeedItem>) => {
          set((state) => ({
            items: state.items.map((item) =>
              item.id === itemId ? { ...item, ...updates } : item
            ),
          }))
        },

        removeItem: (itemId: string) => {
          set((state) => ({
            items: state.items.filter((item) => item.id !== itemId),
          }))
        },

        setCurrentIndex: (index: number) => {
          set({ currentIndex: index })
        },

        setLoading: (loading: boolean) => {
          set({ loading })
        },

        setError: (error: string | null) => {
          set({ error })
        },

        setHasMore: (hasMore: boolean) => {
          set({ hasMore })
        },

        setNextCursor: (cursor: string | null) => {
          set({ nextCursor: cursor })
        },

        setFilters: (newFilters: Partial<FeedFilters>) => {
          set((state) => ({
            filters: { ...state.filters, ...newFilters },
          }))
        },

        setPreferences: (newPreferences: Partial<FeedPreferences>) => {
          set((state) => ({
            preferences: { ...state.preferences, ...newPreferences },
          }))
        },

        addPreloadedItem: (itemId: string) => {
          set((state) => ({
            preloadedItems: new Set([...state.preloadedItems, itemId]),
          }))
        },

        removePreloadedItem: (itemId: string) => {
          set((state) => {
            const newSet = new Set(state.preloadedItems)
            newSet.delete(itemId)
            return { preloadedItems: newSet }
          })
        },

        clearFeed: () => {
          set({
            items: [],
            currentIndex: 0,
            hasMore: true,
            nextCursor: null,
            error: null,
            preloadedItems: new Set(),
          })
        },

        cacheFeed: (feedType: FeedType, items: FeedItem[]) => {
          set((state) => {
            const newCache = new Map(state.feedCache)
            const newFetchTime = new Map(state.lastFetchTime)
            newCache.set(feedType, items)
            newFetchTime.set(feedType, Date.now())
            return { feedCache: newCache, lastFetchTime: newFetchTime }
          })
        },

        getCachedFeed: (feedType: FeedType) => {
          const { feedCache, lastFetchTime } = get()
          const cached = feedCache.get(feedType)
          const fetchTime = lastFetchTime.get(feedType)
          
          // Cache expires after 5 minutes
          if (cached && fetchTime && Date.now() - fetchTime < 5 * 60 * 1000) {
            return cached
          }
          return null
        },

        clearCache: () => {
          set({ feedCache: new Map(), lastFetchTime: new Map() })
        },

        reset: () => {
          set({
            currentFeed: FeedType.DISCOVER,
            items: [],
            loading: false,
            error: null,
            hasMore: true,
            nextCursor: null,
            currentIndex: 0,
            preloadedItems: new Set(),
            filters: defaultFilters,
            feedCache: new Map(),
            lastFetchTime: new Map(),
          })
        },
      }),
      {
        name: 'hvppy-feed-store',
        partialize: (state) => ({
          preferences: state.preferences,
          filters: state.filters,
          currentFeed: state.currentFeed,
        }),
      }
    ),
    {
      name: 'hvppy-feed-store',
    }
  )
)

// Selectors for optimized re-renders
export const selectCurrentItem = (state: FeedState) =>
  state.items[state.currentIndex] || null

export const selectIsItemPreloaded = (itemId: string) => (state: FeedState) =>
  state.preloadedItems.has(itemId)

export const selectFeedItems = (state: FeedState) => state.items
export const selectCurrentIndex = (state: FeedState) => state.currentIndex
export const selectLoading = (state: FeedState) => state.loading
export const selectHasMore = (state: FeedState) => state.hasMore
export const selectFilters = (state: FeedState) => state.filters
export const selectPreferences = (state: FeedState) => state.preferences
