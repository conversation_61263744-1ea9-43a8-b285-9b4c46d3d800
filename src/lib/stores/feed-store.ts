import { create } from 'zustand'
import { devtools, persist, subscribeWithSelector } from 'zustand/middleware'
import { FeedType, FeedItem, FeedFilters, FeedPreferences, InteractionData } from '@/types/feed'

interface VirtualScrollState {
  visibleRange: { start: number; end: number }
  itemHeight: number
  containerHeight: number
  scrollTop: number
  overscan: number
}

interface ContentDiscoveryState {
  moodScores: Map<string, number>
  userPreferences: Map<string, number>
  engagementHistory: Map<string, number>
  discoveryAlgorithm: 'mood' | 'engagement' | 'trending' | 'personalized'
}

interface OptimisticUpdate {
  id: string
  type: 'like' | 'unlike' | 'follow' | 'unfollow' | 'share' | 'comment'
  timestamp: number
  data: any
  rollback?: () => void
}

interface FeedState {
  // Current feed state
  currentFeed: FeedType
  items: FeedItem[]
  loading: boolean
  error: string | null
  hasMore: boolean
  nextCursor: string | null

  // Enhanced feed management
  currentIndex: number
  preloadedItems: Set<string>
  filters: FeedFilters
  preferences: FeedPreferences

  // Virtual scrolling
  virtualScroll: VirtualScrollState

  // Content discovery
  discovery: ContentDiscoveryState

  // Optimistic updates
  optimisticUpdates: Map<string, OptimisticUpdate>

  // Performance metrics
  metrics: {
    fps: number
    memoryUsage: number
    loadTime: number
    scrollPerformance: number
  }

  // Cache management
  feedCache: Map<string, FeedItem[]>
  lastFetchTime: Map<string, number>
  
  // Enhanced Actions
  setCurrentFeed: (feedType: FeedType) => void
  setItems: (items: FeedItem[]) => void
  addItems: (items: FeedItem[]) => void
  updateItem: (itemId: string, updates: Partial<FeedItem>) => void
  removeItem: (itemId: string) => void
  setCurrentIndex: (index: number) => void
  setLoading: (loading: boolean) => void
  setError: (error: string | null) => void
  setHasMore: (hasMore: boolean) => void
  setNextCursor: (cursor: string | null) => void

  // Preloading management
  addPreloadedItem: (itemId: string) => void
  removePreloadedItem: (itemId: string) => void
  preloadContent: (items: FeedItem[], startIndex: number, distance: number) => Promise<void>
  offloadContent: (currentIndex: number, distance: number) => void

  // Virtual scrolling
  updateVirtualScroll: (updates: Partial<VirtualScrollState>) => void
  calculateVisibleRange: (scrollTop: number, containerHeight: number) => void
  getVirtualItems: () => FeedItem[]

  // Content discovery
  updateMoodScore: (mood: string, score: number) => void
  updateUserPreference: (contentType: string, score: number) => void
  recordEngagement: (itemId: string, type: string, duration?: number) => void
  getPersonalizedFeed: () => FeedItem[]

  // Optimistic updates
  addOptimisticUpdate: (update: OptimisticUpdate) => void
  removeOptimisticUpdate: (id: string) => void
  rollbackOptimisticUpdate: (id: string) => void
  applyOptimisticUpdates: () => void

  // Performance monitoring
  updateMetrics: (metrics: Partial<FeedState['metrics']>) => void
  getPerformanceReport: () => FeedState['metrics']

  // Enhanced filtering and preferences
  setFilters: (filters: Partial<FeedFilters>) => void
  setPreferences: (preferences: Partial<FeedPreferences>) => void
  clearFeed: () => void

  // Cache management
  cacheFeed: (feedType: FeedType, items: FeedItem[]) => void
  getCachedFeed: (feedType: FeedType) => FeedItem[] | null
  clearCache: () => void
  reset: () => void
}

const defaultPreferences: FeedPreferences = {
  autoPlay: true,
  muteByDefault: true,
  showCaptions: false,
  preferredQuality: 'auto',
  enableHapticFeedback: true,
  preloadDistance: 3,
  offloadDistance: 10,
}

const defaultFilters: FeedFilters = {
  moods: [],
  contentTypes: [],
  creators: [],
  personas: [],
  isExperimental: false,
}

const defaultVirtualScroll: VirtualScrollState = {
  visibleRange: { start: 0, end: 10 },
  itemHeight: window?.innerHeight || 800,
  containerHeight: window?.innerHeight || 800,
  scrollTop: 0,
  overscan: 2,
}

const defaultDiscovery: ContentDiscoveryState = {
  moodScores: new Map(),
  userPreferences: new Map(),
  engagementHistory: new Map(),
  discoveryAlgorithm: 'personalized',
}

const defaultMetrics = {
  fps: 60,
  memoryUsage: 0,
  loadTime: 0,
  scrollPerformance: 100,
}

export const useFeedStore = create<FeedState>()(
  devtools(
    subscribeWithSelector(
      persist(
        (set, get) => ({
          // Initial state
          currentFeed: FeedType.DISCOVER,
          items: [],
          loading: false,
          error: null,
          hasMore: true,
          nextCursor: null,
          currentIndex: 0,
          preloadedItems: new Set(),
          filters: defaultFilters,
          preferences: defaultPreferences,

          // Enhanced state
          virtualScroll: defaultVirtualScroll,
          discovery: defaultDiscovery,
          optimisticUpdates: new Map(),
          metrics: defaultMetrics,

          feedCache: new Map(),
          lastFetchTime: new Map(),

        // Actions
        setCurrentFeed: (feedType: FeedType) => {
          set({ currentFeed: feedType, currentIndex: 0 })
        },

        setItems: (items: FeedItem[]) => {
          set({ items, currentIndex: 0, preloadedItems: new Set() })
        },

        addItems: (newItems: FeedItem[]) => {
          set((state) => ({
            items: [...state.items, ...newItems],
          }))
        },

        updateItem: (itemId: string, updates: Partial<FeedItem>) => {
          set((state) => ({
            items: state.items.map((item) =>
              item.id === itemId ? { ...item, ...updates } : item
            ),
          }))
        },

        removeItem: (itemId: string) => {
          set((state) => ({
            items: state.items.filter((item) => item.id !== itemId),
          }))
        },

        setCurrentIndex: (index: number) => {
          set({ currentIndex: index })
        },

        setLoading: (loading: boolean) => {
          set({ loading })
        },

        setError: (error: string | null) => {
          set({ error })
        },

        setHasMore: (hasMore: boolean) => {
          set({ hasMore })
        },

        setNextCursor: (cursor: string | null) => {
          set({ nextCursor: cursor })
        },

        setFilters: (newFilters: Partial<FeedFilters>) => {
          set((state) => ({
            filters: { ...state.filters, ...newFilters },
          }))
        },

        setPreferences: (newPreferences: Partial<FeedPreferences>) => {
          set((state) => ({
            preferences: { ...state.preferences, ...newPreferences },
          }))
        },

        addPreloadedItem: (itemId: string) => {
          set((state) => ({
            preloadedItems: new Set([...state.preloadedItems, itemId]),
          }))
        },

        removePreloadedItem: (itemId: string) => {
          set((state) => {
            const newSet = new Set(state.preloadedItems)
            newSet.delete(itemId)
            return { preloadedItems: newSet }
          })
        },

        clearFeed: () => {
          set({
            items: [],
            currentIndex: 0,
            hasMore: true,
            nextCursor: null,
            error: null,
            preloadedItems: new Set(),
          })
        },

        cacheFeed: (feedType: FeedType, items: FeedItem[]) => {
          set((state) => {
            const newCache = new Map(state.feedCache)
            const newFetchTime = new Map(state.lastFetchTime)
            newCache.set(feedType, items)
            newFetchTime.set(feedType, Date.now())
            return { feedCache: newCache, lastFetchTime: newFetchTime }
          })
        },

        getCachedFeed: (feedType: FeedType) => {
          const { feedCache, lastFetchTime } = get()
          const cached = feedCache.get(feedType)
          const fetchTime = lastFetchTime.get(feedType)
          
          // Cache expires after 5 minutes
          if (cached && fetchTime && Date.now() - fetchTime < 5 * 60 * 1000) {
            return cached
          }
          return null
        },

        clearCache: () => {
          set({ feedCache: new Map(), lastFetchTime: new Map() })
        },

        // Enhanced preloading management
        preloadContent: async (items: FeedItem[], startIndex: number, distance: number) => {
          const { preloadedItems } = get()
          const endIndex = Math.min(startIndex + distance, items.length)

          for (let i = startIndex; i < endIndex; i++) {
            const item = items[i]
            if (!preloadedItems.has(item.id)) {
              // Preload media content
              if (item.post.mediaUrls?.length > 0) {
                try {
                  await Promise.all(
                    item.post.mediaUrls.map(url => {
                      if (url.includes('image')) {
                        const img = new Image()
                        img.src = url
                        return new Promise(resolve => {
                          img.onload = resolve
                          img.onerror = resolve
                        })
                      } else if (url.includes('video')) {
                        const video = document.createElement('video')
                        video.src = url
                        video.preload = 'metadata'
                        return Promise.resolve()
                      }
                      return Promise.resolve()
                    })
                  )

                  set((state) => ({
                    preloadedItems: new Set([...state.preloadedItems, item.id])
                  }))
                } catch (error) {
                  console.warn(`Failed to preload content for item ${item.id}:`, error)
                }
              }
            }
          }
        },

        offloadContent: (currentIndex: number, distance: number) => {
          set((state) => {
            const newPreloadedItems = new Set(state.preloadedItems)
            state.items.forEach((item, index) => {
              if (Math.abs(index - currentIndex) > distance) {
                newPreloadedItems.delete(item.id)
              }
            })
            return { preloadedItems: newPreloadedItems }
          })
        },

        // Virtual scrolling
        updateVirtualScroll: (updates: Partial<VirtualScrollState>) => {
          set((state) => ({
            virtualScroll: { ...state.virtualScroll, ...updates }
          }))
        },

        calculateVisibleRange: (scrollTop: number, containerHeight: number) => {
          const { virtualScroll, items } = get()
          const { itemHeight, overscan } = virtualScroll

          const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan)
          const endIndex = Math.min(
            items.length - 1,
            Math.ceil((scrollTop + containerHeight) / itemHeight) + overscan
          )

          set((state) => ({
            virtualScroll: {
              ...state.virtualScroll,
              visibleRange: { start: startIndex, end: endIndex },
              scrollTop,
              containerHeight
            }
          }))
        },

        getVirtualItems: () => {
          const { items, virtualScroll } = get()
          const { visibleRange } = virtualScroll
          return items.slice(visibleRange.start, visibleRange.end + 1)
        },

        // Content discovery
        updateMoodScore: (mood: string, score: number) => {
          set((state) => {
            const newMoodScores = new Map(state.discovery.moodScores)
            newMoodScores.set(mood, score)
            return {
              discovery: {
                ...state.discovery,
                moodScores: newMoodScores
              }
            }
          })
        },

        updateUserPreference: (contentType: string, score: number) => {
          set((state) => {
            const newPreferences = new Map(state.discovery.userPreferences)
            newPreferences.set(contentType, score)
            return {
              discovery: {
                ...state.discovery,
                userPreferences: newPreferences
              }
            }
          })
        },

        recordEngagement: (itemId: string, type: string, duration?: number) => {
          set((state) => {
            const newEngagement = new Map(state.discovery.engagementHistory)
            const currentScore = newEngagement.get(itemId) || 0
            let scoreIncrease = 1

            switch (type) {
              case 'view':
                scoreIncrease = duration ? Math.min(duration / 1000, 10) : 1
                break
              case 'like':
                scoreIncrease = 5
                break
              case 'share':
                scoreIncrease = 10
                break
              case 'comment':
                scoreIncrease = 8
                break
            }

            newEngagement.set(itemId, currentScore + scoreIncrease)
            return {
              discovery: {
                ...state.discovery,
                engagementHistory: newEngagement
              }
            }
          })
        },

        getPersonalizedFeed: () => {
          const { items, discovery } = get()
          const { moodScores, userPreferences, engagementHistory } = discovery

          return items.sort((a, b) => {
            let scoreA = 0
            let scoreB = 0

            // Mood scoring
            a.post.moods.forEach(mood => {
              scoreA += moodScores.get(mood) || 0
            })
            b.post.moods.forEach(mood => {
              scoreB += moodScores.get(mood) || 0
            })

            // Content type preference
            scoreA += userPreferences.get(a.post.contentType) || 0
            scoreB += userPreferences.get(b.post.contentType) || 0

            // Engagement history
            scoreA += engagementHistory.get(a.id) || 0
            scoreB += engagementHistory.get(b.id) || 0

            return scoreB - scoreA
          })
        },

        // Optimistic updates
        addOptimisticUpdate: (update: OptimisticUpdate) => {
          set((state) => {
            const newUpdates = new Map(state.optimisticUpdates)
            newUpdates.set(update.id, update)
            return { optimisticUpdates: newUpdates }
          })
        },

        removeOptimisticUpdate: (id: string) => {
          set((state) => {
            const newUpdates = new Map(state.optimisticUpdates)
            newUpdates.delete(id)
            return { optimisticUpdates: newUpdates }
          })
        },

        rollbackOptimisticUpdate: (id: string) => {
          const { optimisticUpdates } = get()
          const update = optimisticUpdates.get(id)
          if (update?.rollback) {
            update.rollback()
          }
          set((state) => {
            const newUpdates = new Map(state.optimisticUpdates)
            newUpdates.delete(id)
            return { optimisticUpdates: newUpdates }
          })
        },

        applyOptimisticUpdates: () => {
          const { optimisticUpdates, items } = get()

          const updatedItems = items.map(item => {
            let updatedItem = { ...item }

            optimisticUpdates.forEach(update => {
              if (update.type === 'like' && update.data.postId === item.post.id) {
                updatedItem.post.likeCount = (updatedItem.post.likeCount || 0) + 1
              } else if (update.type === 'unlike' && update.data.postId === item.post.id) {
                updatedItem.post.likeCount = Math.max(0, (updatedItem.post.likeCount || 0) - 1)
              }
            })

            return updatedItem
          })

          set({ items: updatedItems })
        },

        // Performance monitoring
        updateMetrics: (metrics: Partial<FeedState['metrics']>) => {
          set((state) => ({
            metrics: { ...state.metrics, ...metrics }
          }))
        },

        getPerformanceReport: () => {
          const { metrics } = get()
          return metrics
        },

        reset: () => {
          set({
            currentFeed: FeedType.DISCOVER,
            items: [],
            loading: false,
            error: null,
            hasMore: true,
            nextCursor: null,
            currentIndex: 0,
            preloadedItems: new Set(),
            filters: defaultFilters,
            preferences: defaultPreferences,
            virtualScroll: defaultVirtualScroll,
            discovery: defaultDiscovery,
            optimisticUpdates: new Map(),
            metrics: defaultMetrics,
            feedCache: new Map(),
            lastFetchTime: new Map(),
          })
        },
      }),
      {
        name: 'hvppy-feed-store',
        partialize: (state) => ({
          preferences: state.preferences,
          filters: state.filters,
          currentFeed: state.currentFeed,
        }),
      }
    ),
    {
      name: 'hvppy-feed-store',
    }
  )
)

// Enhanced selectors for optimized re-renders
export const selectCurrentItem = (state: FeedState) =>
  state.items[state.currentIndex] || null

export const selectIsItemPreloaded = (itemId: string) => (state: FeedState) =>
  state.preloadedItems.has(itemId)

export const selectFeedItems = (state: FeedState) => state.items
export const selectCurrentIndex = (state: FeedState) => state.currentIndex
export const selectLoading = (state: FeedState) => state.loading
export const selectHasMore = (state: FeedState) => state.hasMore
export const selectFilters = (state: FeedState) => state.filters
export const selectPreferences = (state: FeedState) => state.preferences

// Virtual scrolling selectors
export const selectVirtualScroll = (state: FeedState) => state.virtualScroll
export const selectVisibleItems = (state: FeedState) => {
  const { items, virtualScroll } = state
  const { visibleRange } = virtualScroll
  return items.slice(visibleRange.start, visibleRange.end + 1)
}

// Content discovery selectors
export const selectDiscovery = (state: FeedState) => state.discovery
export const selectMoodScores = (state: FeedState) => state.discovery.moodScores
export const selectUserPreferences = (state: FeedState) => state.discovery.userPreferences
export const selectEngagementHistory = (state: FeedState) => state.discovery.engagementHistory

// Performance selectors
export const selectMetrics = (state: FeedState) => state.metrics
export const selectOptimisticUpdates = (state: FeedState) => state.optimisticUpdates

// Computed selectors
export const selectPersonalizedItems = (state: FeedState) => {
  const { items, discovery } = state
  const { moodScores, userPreferences, engagementHistory } = discovery

  return items.map(item => ({
    ...item,
    personalizedScore:
      item.post.moods.reduce((score, mood) => score + (moodScores.get(mood) || 0), 0) +
      (userPreferences.get(item.post.contentType) || 0) +
      (engagementHistory.get(item.id) || 0)
  })).sort((a, b) => b.personalizedScore - a.personalizedScore)
}
