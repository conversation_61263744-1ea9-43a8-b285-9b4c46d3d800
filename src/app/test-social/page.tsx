"use client"

import React, { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { 
  SocialInteractions,
  FollowButton,
  UserSuggestions,
  Notifications,
  ContentCreator,
} from '@/components/social'
import { PerformanceDashboard } from '@/components/feed/performance-dashboard'
import { VerticalFeedContainer } from '@/components/feed/vertical-feed-container'
import { 
  TestTube, 
  Users, 
  Heart, 
  MessageCircle, 
  Share2,
  Bookmark,
  Bell,
  Activity,
  Sparkles,
  CheckCircle,
  AlertCircle
} from 'lucide-react'
import { FeedType, ContentType } from '@/types'
import { toast } from 'sonner'

export default function TestSocialPage() {
  const [testResults, setTestResults] = useState<Record<string, boolean>>({})
  const [isRunningTests, setIsRunningTests] = useState(false)

  const runTest = async (testName: string, testFn: () => Promise<boolean> | boolean) => {
    try {
      const result = await testFn()
      setTestResults(prev => ({ ...prev, [testName]: result }))
      return result
    } catch (error) {
      console.error(`Test ${testName} failed:`, error)
      setTestResults(prev => ({ ...prev, [testName]: false }))
      return false
    }
  }

  const runAllTests = async () => {
    setIsRunningTests(true)
    setTestResults({})

    const tests = [
      {
        name: 'Social Interactions',
        test: () => {
          // Test if social interactions component renders
          const element = document.querySelector('[data-testid="social-interactions"]')
          return !!element
        }
      },
      {
        name: 'Follow System',
        test: () => {
          // Test if follow button renders
          const element = document.querySelector('[data-testid="follow-button"]')
          return !!element
        }
      },
      {
        name: 'User Suggestions',
        test: () => {
          // Test if user suggestions component renders
          const element = document.querySelector('[data-testid="user-suggestions"]')
          return !!element
        }
      },
      {
        name: 'Notifications',
        test: () => {
          // Test if notifications component renders
          const element = document.querySelector('[data-testid="notifications"]')
          return !!element
        }
      },
      {
        name: 'Content Creator',
        test: () => {
          // Test if content creator component renders
          const element = document.querySelector('[data-testid="content-creator"]')
          return !!element
        }
      },
      {
        name: 'Performance Monitor',
        test: () => {
          // Test if performance dashboard renders
          const element = document.querySelector('[data-testid="performance-dashboard"]')
          return !!element
        }
      },
      {
        name: 'Feed Container',
        test: () => {
          // Test if vertical feed container renders
          const element = document.querySelector('[data-testid="vertical-feed"]')
          return !!element
        }
      },
      {
        name: 'State Management',
        test: () => {
          // Test if Zustand stores are accessible
          try {
            const { useFeedStore } = require('@/lib/stores/feed-store')
            const { useSocialStore } = require('@/lib/stores/social-store')
            const { useInteractionStore } = require('@/lib/stores/interaction-store')
            return !!(useFeedStore && useSocialStore && useInteractionStore)
          } catch {
            return false
          }
        }
      }
    ]

    for (const test of tests) {
      await runTest(test.name, test.test)
      await new Promise(resolve => setTimeout(resolve, 500)) // Small delay between tests
    }

    setIsRunningTests(false)
    
    const passedTests = Object.values(testResults).filter(Boolean).length
    const totalTests = tests.length
    
    if (passedTests === totalTests) {
      toast.success(`All ${totalTests} tests passed! 🎉`)
    } else {
      toast.error(`${passedTests}/${totalTests} tests passed`)
    }
  }

  const mockPostData = {
    id: 'test-post-1',
    likeCount: 42,
    commentCount: 8,
    shareCount: 15,
  }

  const mockUserData = {
    id: 'test-user-1',
    username: 'testuser',
    displayName: 'Test User',
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-7xl">
      <div className="mb-8">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-3">
            <TestTube className="h-8 w-8 text-purple-500" />
            <div>
              <h1 className="text-3xl font-bold">Social Media System Test</h1>
              <p className="text-muted-foreground">
                Comprehensive testing of all social media features and components
              </p>
            </div>
          </div>
          <Button onClick={runAllTests} disabled={isRunningTests}>
            {isRunningTests ? 'Running Tests...' : 'Run All Tests'}
          </Button>
        </div>

        {/* Test Results */}
        {Object.keys(testResults).length > 0 && (
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                Test Results
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                {Object.entries(testResults).map(([testName, passed]) => (
                  <div
                    key={testName}
                    className="flex items-center gap-2 p-2 rounded border"
                  >
                    {passed ? (
                      <CheckCircle className="h-4 w-4 text-green-500" />
                    ) : (
                      <AlertCircle className="h-4 w-4 text-red-500" />
                    )}
                    <span className="text-sm">{testName}</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      <Tabs defaultValue="components" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="components">Components</TabsTrigger>
          <TabsTrigger value="interactions">Interactions</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="integration">Integration</TabsTrigger>
        </TabsList>

        <TabsContent value="components" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Social Interactions Test */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Heart className="h-5 w-5" />
                  Social Interactions
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div data-testid="social-interactions">
                  <SocialInteractions
                    postId={mockPostData.id}
                    likeCount={mockPostData.likeCount}
                    commentCount={mockPostData.commentCount}
                    shareCount={mockPostData.shareCount}
                    variant="default"
                    showCounts={true}
                    showLabels={true}
                  />
                </div>
              </CardContent>
            </Card>

            {/* Follow Button Test */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  Follow System
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div data-testid="follow-button" className="space-y-3">
                  <FollowButton
                    userId={mockUserData.id}
                    username={mockUserData.username}
                    displayName={mockUserData.displayName}
                  />
                  <div className="text-sm text-muted-foreground">
                    Test follow/unfollow functionality
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* User Suggestions Test */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  User Discovery
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div data-testid="user-suggestions">
                  <UserSuggestions
                    title="Test Suggestions"
                    limit={3}
                    variant="list"
                    showStats={true}
                  />
                </div>
              </CardContent>
            </Card>

            {/* Notifications Test */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Bell className="h-5 w-5" />
                  Notifications
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div data-testid="notifications">
                  <Notifications
                    variant="sidebar"
                    maxHeight="300px"
                    showHeader={true}
                  />
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="interactions" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Sparkles className="h-5 w-5" />
                Content Creation
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div data-testid="content-creator">
                <ContentCreator
                  defaultMood="Happy"
                  defaultContentType={ContentType.TEXT}
                  onPublish={(content) => {
                    toast.success('Test content published!')
                    console.log('Published content:', content)
                  }}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="performance" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div data-testid="performance-dashboard">
              <PerformanceDashboard
                variant="full"
                showRecommendations={true}
                enableAutoOptimization={true}
              />
            </div>
            
            <Card>
              <CardHeader>
                <CardTitle>Performance Metrics</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span>Target FPS:</span>
                    <Badge>60 FPS</Badge>
                  </div>
                  <div className="flex justify-between">
                    <span>Memory Threshold:</span>
                    <Badge>100 MB</Badge>
                  </div>
                  <div className="flex justify-between">
                    <span>Optimization:</span>
                    <Badge variant="outline">Auto-enabled</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="integration" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Vertical Feed Integration</CardTitle>
            </CardHeader>
            <CardContent>
              <div data-testid="vertical-feed" className="h-96 border rounded-lg overflow-hidden">
                <VerticalFeedContainer
                  feedType={FeedType.DISCOVER}
                  showControls={true}
                  enableSwipeGestures={true}
                  preloadDistance={2}
                  offloadDistance={5}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
