"use client"

import React, { useState, useEffect } from 'react'
import { SocialLayout } from '@/components/layout/social-layout'
import { DiscoveryDashboard } from '@/components/discovery/discovery-dashboard'
import { ImmersiveContentCard } from '@/components/feed/immersive-content-card'
import { ModernVerticalFeedContainer } from '@/components/feed/modern-vertical-feed-container'
import { MoodSelector } from '@/components/discovery/mood-selector'
import { PerformanceDashboard } from '@/components/feed/performance-dashboard'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  Sparkles, 
  TrendingUp, 
  Users, 
  Heart, 
  Compass,
  Zap,
  Brain,
  Target,
  BarChart3,
  Settings,
  Play,
  Pause,
  Volume2,
  VolumeX
} from 'lucide-react'
import { FeedType, FeedItem } from '@/types/feed'
import { ContentType } from '@/types'
import { motion, AnimatePresence } from 'framer-motion'
import { useContentDiscovery } from '@/hooks/discovery/use-content-discovery'

export default function EnhancedSocialPage() {
  const [activeTab, setActiveTab] = useState('discover')
  const [selectedMoods, setSelectedMoods] = useState<string[]>(['happy', 'energetic'])
  const [discoveredContent, setDiscoveredContent] = useState<FeedItem[]>([])
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isPlaying, setIsPlaying] = useState(true)
  const [showPerformance, setShowPerformance] = useState(false)

  const { discoverContent, isLoading } = useContentDiscovery()

  // Mock content for demonstration
  const mockContent: FeedItem[] = [
    {
      id: 'demo-1',
      type: 'post',
      post: {
        id: 'demo-1',
        title: 'Amazing Sunset Vibes',
        content: 'Caught this incredible sunset today! The colors were absolutely magical. Sometimes nature just takes your breath away. 🌅✨',
        contentType: ContentType.IMAGE,
        contentUrl: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&h=1200&fit=crop',
        thumbnailUrl: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=600&fit=crop',
        mood: ['happy', 'peaceful', 'nostalgic'],
        createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000),
        publishedAt: new Date(Date.now() - 2 * 60 * 60 * 1000),
        likeCount: 1247,
        commentCount: 89,
        shareCount: 156,
        user: {
          id: 'user-1',
          username: 'naturelover',
          displayName: 'Sarah Chen',
          avatarUrl: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face',
          isVerified: true,
        },
      },
      metadata: {
        algorithm: 'mood_based',
        score: 0.95,
      },
    },
    {
      id: 'demo-2',
      type: 'post',
      post: {
        id: 'demo-2',
        title: 'Creative Flow State',
        content: 'When inspiration strikes and everything just flows... This is what pure creativity feels like! 🎨💫',
        contentType: ContentType.TEXT,
        mood: ['creative', 'energetic', 'inspired'],
        createdAt: new Date(Date.now() - 4 * 60 * 60 * 1000),
        publishedAt: new Date(Date.now() - 4 * 60 * 60 * 1000),
        likeCount: 892,
        commentCount: 67,
        shareCount: 234,
        user: {
          id: 'user-2',
          username: 'artflow',
          displayName: 'Alex Rivera',
          avatarUrl: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face',
          isVerified: false,
        },
      },
      metadata: {
        algorithm: 'engagement_based',
        score: 0.88,
      },
    },
    {
      id: 'demo-3',
      type: 'post',
      post: {
        id: 'demo-3',
        title: 'Midnight Thoughts',
        content: 'Sometimes the best ideas come in the quiet moments between sleep and dreams. What keeps you up at night? 🌙💭',
        contentType: ContentType.TEXT,
        mood: ['mysterious', 'contemplative', 'peaceful'],
        createdAt: new Date(Date.now() - 6 * 60 * 60 * 1000),
        publishedAt: new Date(Date.now() - 6 * 60 * 60 * 1000),
        likeCount: 567,
        commentCount: 123,
        shareCount: 89,
        user: {
          id: 'user-3',
          username: 'nightthinker',
          displayName: 'Maya Patel',
          avatarUrl: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=face',
          isVerified: true,
        },
      },
      metadata: {
        algorithm: 'similarity_based',
        score: 0.76,
      },
    },
  ]

  useEffect(() => {
    setDiscoveredContent(mockContent)
  }, [])

  const handleContentSelect = (content: FeedItem[]) => {
    setDiscoveredContent(content.length > 0 ? content : mockContent)
    setCurrentIndex(0)
  }

  const handleNext = () => {
    setCurrentIndex((prev) => (prev + 1) % discoveredContent.length)
  }

  const handlePrevious = () => {
    setCurrentIndex((prev) => (prev - 1 + discoveredContent.length) % discoveredContent.length)
  }

  const currentItem = discoveredContent[currentIndex]

  return (
    <SocialLayout showSidebar={true}>
      <div className="min-h-screen bg-gradient-to-br from-purple-50 via-pink-50 to-blue-50 dark:from-gray-900 dark:via-purple-900/20 dark:to-gray-900">
        <div className="container mx-auto px-4 py-6">
          <div className="grid grid-cols-1 xl:grid-cols-4 gap-6 h-screen">
            {/* Discovery Panel */}
            <div className="xl:col-span-1 space-y-4 overflow-y-auto">
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5 }}
              >
                <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-xl">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <div className="p-2 bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg">
                        <Sparkles className="h-4 w-4 text-white" />
                      </div>
                      Discovery Hub
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <Tabs value={activeTab} onValueChange={setActiveTab}>
                      <TabsList className="grid w-full grid-cols-3">
                        <TabsTrigger value="discover" className="gap-1">
                          <Compass className="h-3 w-3" />
                          <span className="hidden sm:inline">Discover</span>
                        </TabsTrigger>
                        <TabsTrigger value="mood" className="gap-1">
                          <Heart className="h-3 w-3" />
                          <span className="hidden sm:inline">Mood</span>
                        </TabsTrigger>
                        <TabsTrigger value="trending" className="gap-1">
                          <TrendingUp className="h-3 w-3" />
                          <span className="hidden sm:inline">Trending</span>
                        </TabsTrigger>
                      </TabsList>

                      <TabsContent value="discover" className="space-y-4">
                        <div className="space-y-3">
                          <h3 className="font-medium">AI Recommendations</h3>
                          <div className="grid grid-cols-2 gap-2">
                            <Button variant="outline" size="sm" className="gap-2">
                              <Brain className="h-3 w-3" />
                              Smart Feed
                            </Button>
                            <Button variant="outline" size="sm" className="gap-2">
                              <Target className="h-3 w-3" />
                              For You
                            </Button>
                          </div>
                        </div>
                      </TabsContent>

                      <TabsContent value="mood" className="space-y-4">
                        <MoodSelector
                          selectedMoods={selectedMoods}
                          onMoodChange={setSelectedMoods}
                          variant="compact"
                          maxSelections={3}
                        />
                      </TabsContent>

                      <TabsContent value="trending" className="space-y-4">
                        <div className="space-y-3">
                          <div className="flex items-center gap-2">
                            <TrendingUp className="h-4 w-4 text-orange-500" />
                            <span className="font-medium">Hot Right Now</span>
                          </div>
                          <div className="space-y-2">
                            {['#SunsetVibes', '#CreativeFlow', '#MidnightThoughts'].map((tag, index) => (
                              <div key={index} className="flex items-center justify-between p-2 bg-muted/50 rounded">
                                <span className="text-sm">{tag}</span>
                                <Badge variant="secondary" className="text-xs">
                                  {Math.floor(Math.random() * 100)}K
                                </Badge>
                              </div>
                            ))}
                          </div>
                        </div>
                      </TabsContent>
                    </Tabs>
                  </CardContent>
                </Card>
              </motion.div>

              {/* Performance Monitor */}
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: 0.2 }}
              >
                <PerformanceDashboard
                  variant="compact"
                  showRecommendations={false}
                />
              </motion.div>
            </div>

            {/* Main Content Feed */}
            <div className="xl:col-span-2 relative">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.1 }}
                className="h-full"
              >
                <div className="h-[calc(100vh-120px)] rounded-lg overflow-hidden bg-black">
                  <ModernVerticalFeedContainer
                    feedType={FeedType.DISCOVER}
                    enableVirtualScrolling={true}
                    enablePerformanceMonitoring={true}
                    enableRealTimeUpdates={true}
                    maxConcurrentPlayers={1}
                    filters={{ moods: selectedMoods }}
                    onInteraction={(type, data) => {
                      console.log('Enhanced Feed Interaction:', type, data)
                    }}
                    onItemChange={(item, index) => {
                      setCurrentIndex(index)
                      console.log('Item changed:', item?.post?.title, index)
                    }}
                    className="h-full w-full"
                  />
                </div>

                {/* Navigation Controls */}
                <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex items-center gap-3 bg-black/20 backdrop-blur-sm rounded-full px-4 py-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handlePrevious}
                    className="text-white hover:bg-white/20 rounded-full"
                  >
                    ←
                  </Button>
                  
                  <div className="flex items-center gap-1">
                    {discoveredContent.map((_, index) => (
                      <div
                        key={index}
                        className={`w-2 h-2 rounded-full transition-colors ${
                          index === currentIndex ? 'bg-white' : 'bg-white/40'
                        }`}
                      />
                    ))}
                  </div>
                  
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleNext}
                    className="text-white hover:bg-white/20 rounded-full"
                  >
                    →
                  </Button>
                </div>

                {/* Playback Controls */}
                <div className="absolute top-4 left-1/2 transform -translate-x-1/2 flex items-center gap-2 bg-black/20 backdrop-blur-sm rounded-full px-3 py-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setIsPlaying(!isPlaying)}
                    className="text-white hover:bg-white/20 rounded-full"
                  >
                    {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                  </Button>
                  
                  <div className="text-white text-sm">
                    {currentIndex + 1} / {discoveredContent.length}
                  </div>
                </div>
              </motion.div>
            </div>

            {/* Analytics & Insights */}
            <div className="xl:col-span-1 space-y-4 overflow-y-auto">
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: 0.3 }}
              >
                <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-xl">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <BarChart3 className="h-4 w-4" />
                      Content Insights
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {currentItem && (
                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-muted-foreground">Algorithm</span>
                          <Badge variant="outline">
                            {currentItem.metadata?.algorithm || 'Unknown'}
                          </Badge>
                        </div>
                        
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-muted-foreground">Match Score</span>
                          <Badge variant="secondary">
                            {Math.round((currentItem.metadata?.score || 0) * 100)}%
                          </Badge>
                        </div>
                        
                        <div className="space-y-2">
                          <span className="text-sm text-muted-foreground">Mood Tags</span>
                          <div className="flex flex-wrap gap-1">
                            {currentItem.post.mood?.map((mood, index) => (
                              <Badge key={index} variant="outline" className="text-xs">
                                {mood}
                              </Badge>
                            ))}
                          </div>
                        </div>
                        
                        <div className="grid grid-cols-3 gap-2 text-center">
                          <div className="p-2 bg-muted/50 rounded">
                            <div className="text-lg font-bold text-red-500">
                              {currentItem.post.likeCount}
                            </div>
                            <div className="text-xs text-muted-foreground">Likes</div>
                          </div>
                          <div className="p-2 bg-muted/50 rounded">
                            <div className="text-lg font-bold text-blue-500">
                              {currentItem.post.commentCount}
                            </div>
                            <div className="text-xs text-muted-foreground">Comments</div>
                          </div>
                          <div className="p-2 bg-muted/50 rounded">
                            <div className="text-lg font-bold text-green-500">
                              {currentItem.post.shareCount}
                            </div>
                            <div className="text-xs text-muted-foreground">Shares</div>
                          </div>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: 0.4 }}
              >
                <DiscoveryDashboard
                  onContentSelect={handleContentSelect}
                  showAdvancedControls={false}
                />
              </motion.div>
            </div>
          </div>
        </div>
      </div>
    </SocialLayout>
  )
}
