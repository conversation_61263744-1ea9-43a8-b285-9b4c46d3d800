"use client"

import React, { useState, useRef, useEffect } from 'react'
import { cn } from '@/lib/utils'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { SocialInteractions } from '@/components/social/social-interactions'
import { FollowButton } from '@/components/social/follow-button'
import { 
  Play, 
  Pause, 
  Volume2, 
  VolumeX, 
  Verified,
  Clock,
  Eye,
  Sparkles,
  Heart,
  MessageCircle,
  Share2
} from 'lucide-react'
import { FeedItem } from '@/types/feed'
import { motion, AnimatePresence } from 'framer-motion'
import { formatDistanceToNow } from 'date-fns'

interface ImmersiveContentCardProps {
  item: FeedItem
  isActive?: boolean
  isVisible?: boolean
  onPlay?: () => void
  onPause?: () => void
  onInteraction?: (type: string, data?: any) => void
  className?: string
  showControls?: boolean
  autoPlay?: boolean
}

export function ImmersiveContentCard({
  item,
  isActive = false,
  isVisible = false,
  onPlay,
  onPause,
  onInteraction,
  className,
  showControls = true,
  autoPlay = true,
}: ImmersiveContentCardProps) {
  const [isPlaying, setIsPlaying] = useState(false)
  const [isMuted, setIsMuted] = useState(true)
  const [showOverlay, setShowOverlay] = useState(false)
  const [progress, setProgress] = useState(0)
  const [viewCount, setViewCount] = useState(item.post.likeCount + item.post.commentCount)
  
  const videoRef = useRef<HTMLVideoElement>(null)
  const audioRef = useRef<HTMLAudioElement>(null)

  const { post } = item

  // Auto-play logic
  useEffect(() => {
    if (isActive && isVisible && autoPlay) {
      handlePlay()
    } else {
      handlePause()
    }
  }, [isActive, isVisible, autoPlay])

  // Progress tracking
  useEffect(() => {
    const media = videoRef.current || audioRef.current
    if (!media) return

    const updateProgress = () => {
      if (media.duration) {
        setProgress((media.currentTime / media.duration) * 100)
      }
    }

    const handleEnded = () => {
      setIsPlaying(false)
      setProgress(0)
      if (onInteraction) {
        onInteraction('completed', { duration: media.duration })
      }
    }

    media.addEventListener('timeupdate', updateProgress)
    media.addEventListener('ended', handleEnded)

    return () => {
      media.removeEventListener('timeupdate', updateProgress)
      media.removeEventListener('ended', handleEnded)
    }
  }, [onInteraction])

  const handlePlay = () => {
    const media = videoRef.current || audioRef.current
    if (media) {
      media.play()
      setIsPlaying(true)
      if (onPlay) onPlay()
      if (onInteraction) onInteraction('play')
    }
  }

  const handlePause = () => {
    const media = videoRef.current || audioRef.current
    if (media) {
      media.pause()
      setIsPlaying(false)
      if (onPause) onPause()
      if (onInteraction) onInteraction('pause')
    }
  }

  const togglePlay = () => {
    if (isPlaying) {
      handlePause()
    } else {
      handlePlay()
    }
  }

  const toggleMute = () => {
    const media = videoRef.current || audioRef.current
    if (media) {
      media.muted = !media.muted
      setIsMuted(media.muted)
      if (onInteraction) onInteraction('mute', { muted: media.muted })
    }
  }

  const formatViewCount = (count: number) => {
    if (count >= 1000000) {
      return `${(count / 1000000).toFixed(1)}M`
    }
    if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}K`
    }
    return count.toString()
  }

  const getMoodGradient = (moods: string[]) => {
    const moodGradients: Record<string, string> = {
      happy: 'from-yellow-400 via-orange-400 to-red-400',
      energetic: 'from-red-400 via-pink-400 to-purple-400',
      chill: 'from-blue-400 via-cyan-400 to-teal-400',
      creative: 'from-purple-400 via-pink-400 to-indigo-400',
      romantic: 'from-pink-400 via-rose-400 to-red-400',
      nostalgic: 'from-amber-400 via-yellow-400 to-orange-400',
      mysterious: 'from-indigo-400 via-purple-400 to-gray-600',
      social: 'from-green-400 via-teal-400 to-blue-400',
    }
    
    if (moods && moods.length > 0) {
      return moodGradients[moods[0].toLowerCase()] || 'from-purple-500 to-pink-500'
    }
    
    return 'from-purple-500 to-pink-500'
  }

  return (
    <motion.div
      layout
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.9 }}
      transition={{ duration: 0.3 }}
      className={cn('relative w-full h-full group', className)}
      onMouseEnter={() => setShowOverlay(true)}
      onMouseLeave={() => setShowOverlay(false)}
    >
      <Card className="w-full h-full overflow-hidden border-0 shadow-2xl">
        <div className="relative w-full h-full">
          {/* Background Gradient */}
          <div className={cn(
            'absolute inset-0 bg-gradient-to-br opacity-20',
            getMoodGradient(post.mood)
          )} />

          {/* Media Content */}
          {post.contentType === 'VIDEO' && post.contentUrl && (
            <video
              ref={videoRef}
              src={post.contentUrl}
              poster={post.thumbnailUrl}
              className="w-full h-full object-cover"
              muted={isMuted}
              loop
              playsInline
            />
          )}
          
          {post.contentType === 'MUSIC' && post.contentUrl && (
            <>
              <audio ref={audioRef} src={post.contentUrl} />
              <div className={cn(
                'w-full h-full bg-gradient-to-br flex items-center justify-center relative overflow-hidden',
                getMoodGradient(post.mood)
              )}>
                {/* Animated Background */}
                <div className="absolute inset-0">
                  <div className="absolute inset-0 bg-black/20" />
                  <motion.div
                    animate={{
                      scale: isPlaying ? [1, 1.1, 1] : 1,
                      rotate: isPlaying ? [0, 360] : 0,
                    }}
                    transition={{
                      duration: 4,
                      repeat: isPlaying ? Infinity : 0,
                      ease: "linear"
                    }}
                    className="absolute inset-0 bg-gradient-to-r from-white/10 to-transparent"
                  />
                </div>
                
                <div className="text-center text-white z-10">
                  <motion.div
                    animate={{
                      scale: isPlaying ? [1, 1.05, 1] : 1,
                    }}
                    transition={{
                      duration: 2,
                      repeat: isPlaying ? Infinity : 0,
                      ease: "easeInOut"
                    }}
                    className="text-8xl mb-6"
                  >
                    🎵
                  </motion.div>
                  <h3 className="text-2xl font-bold mb-2">{post.title}</h3>
                  <p className="text-lg opacity-90">{post.user.displayName}</p>
                </div>
              </div>
            </>
          )}
          
          {post.contentType === 'IMAGE' && post.contentUrl && (
            <div className="relative w-full h-full">
              <img
                src={post.contentUrl}
                alt={post.title || 'Content'}
                className="w-full h-full object-cover"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent" />
            </div>
          )}
          
          {post.contentType === 'TEXT' && (
            <div className={cn(
              'w-full h-full bg-gradient-to-br flex items-center justify-center p-8 relative',
              getMoodGradient(post.mood)
            )}>
              <div className="text-center text-white z-10 max-w-2xl">
                {post.title && (
                  <motion.h3
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="text-4xl font-bold mb-6"
                  >
                    {post.title}
                  </motion.h3>
                )}
                <motion.p
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.2 }}
                  className="text-xl leading-relaxed"
                >
                  {post.content}
                </motion.p>
              </div>
              
              {/* Decorative Elements */}
              <motion.div
                animate={{ rotate: 360 }}
                transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
                className="absolute top-10 right-10 text-white/20"
              >
                <Sparkles className="h-12 w-12" />
              </motion.div>
              <motion.div
                animate={{ rotate: -360 }}
                transition={{ duration: 25, repeat: Infinity, ease: "linear" }}
                className="absolute bottom-10 left-10 text-white/20"
              >
                <Sparkles className="h-8 w-8" />
              </motion.div>
            </div>
          )}

          {/* Progress Bar */}
          {(post.contentType === 'VIDEO' || post.contentType === 'MUSIC') && (
            <div className="absolute bottom-0 left-0 right-0 h-1 bg-black/20">
              <motion.div
                className="h-full bg-gradient-to-r from-purple-400 to-pink-400"
                style={{ width: `${progress}%` }}
                transition={{ duration: 0.1 }}
              />
            </div>
          )}

          {/* Overlay Controls */}
          <AnimatePresence>
            {(showOverlay || !isPlaying) && showControls && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="absolute inset-0 bg-black/20 flex items-center justify-center"
              >
                {(post.contentType === 'VIDEO' || post.contentType === 'MUSIC') && (
                  <motion.div
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                  >
                    <Button
                      variant="ghost"
                      size="lg"
                      onClick={togglePlay}
                      className="text-white hover:bg-white/20 h-16 w-16 rounded-full"
                    >
                      {isPlaying ? (
                        <Pause className="h-8 w-8" />
                      ) : (
                        <Play className="h-8 w-8 ml-1" />
                      )}
                    </Button>
                  </motion.div>
                )}
              </motion.div>
            )}
          </AnimatePresence>

          {/* User Info */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            className="absolute top-6 left-6 flex items-center gap-3"
          >
            <Avatar className="h-12 w-12 border-2 border-white shadow-lg">
              <AvatarImage src={post.user.avatarUrl} />
              <AvatarFallback className="bg-gradient-to-br from-purple-500 to-pink-500 text-white">
                {post.user.displayName.charAt(0)}
              </AvatarFallback>
            </Avatar>
            <div className="text-white">
              <div className="flex items-center gap-2">
                <span className="font-semibold text-lg">{post.user.displayName}</span>
                {post.user.isVerified && (
                  <Verified className="h-5 w-5 text-blue-400" />
                )}
              </div>
              <div className="flex items-center gap-2 text-sm opacity-90">
                <Clock className="h-3 w-3" />
                <span>{formatDistanceToNow(new Date(post.createdAt), { addSuffix: true })}</span>
                <Eye className="h-3 w-3 ml-2" />
                <span>{formatViewCount(viewCount)} views</span>
              </div>
            </div>
          </motion.div>

          {/* Action Buttons */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            className="absolute top-6 right-6 flex items-center gap-3"
          >
            <FollowButton userId={post.user.id} size="sm" />
            
            {(post.contentType === 'VIDEO' || post.contentType === 'MUSIC') && (
              <Button
                variant="ghost"
                size="sm"
                onClick={toggleMute}
                className="text-white hover:bg-white/20 h-10 w-10 rounded-full"
              >
                {isMuted ? <VolumeX className="h-4 w-4" /> : <Volume2 className="h-4 w-4" />}
              </Button>
            )}
          </motion.div>

          {/* Content Info */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="absolute bottom-6 left-6 right-24 text-white"
          >
            {post.title && (
              <h3 className="font-bold text-2xl mb-3 leading-tight">{post.title}</h3>
            )}
            <p className="text-base opacity-95 line-clamp-3 leading-relaxed">{post.content}</p>
            
            {/* Mood Tags */}
            {post.mood && post.mood.length > 0 && (
              <div className="flex gap-2 mt-4">
                {post.mood.slice(0, 3).map((mood, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: 0.5 + index * 0.1 }}
                  >
                    <Badge
                      className="text-white border-white/30 bg-white/20 backdrop-blur-sm hover:bg-white/30 transition-colors"
                    >
                      <Sparkles className="h-3 w-3 mr-1" />
                      {mood}
                    </Badge>
                  </motion.div>
                ))}
              </div>
            )}
          </motion.div>

          {/* Social Interactions */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.4 }}
            className="absolute bottom-6 right-6"
          >
            <SocialInteractions
              postId={post.id}
              likeCount={post.likeCount}
              commentCount={post.commentCount}
              shareCount={post.shareCount}
              variant="vertical"
              showCounts={true}
              showLabels={false}
            />
          </motion.div>
        </div>
      </Card>
    </motion.div>
  )
}
